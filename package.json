{"name": "deepledger-ai", "version": "1.0.0", "main": "index.js", "scripts": {"test": "tsx src/test-tools.ts", "test:comprehensive": "tsx src/comprehensive-tool-test.ts", "dev": "<PERSON>ra dev", "build": "mastra build", "start": "node .mastra/output/index.mjs", "start:dev": "tsx src/index.ts", "start:telemetry": "node --import=./.mastra/output/instrumentation.mjs .mastra/output/index.mjs", "clean": "rm -rf .mastra dist", "type-check": "tsc --noEmit"}, "keywords": ["accounting", "ai", "mastra", "supabase"], "author": "", "license": "ISC", "description": "AI agent for recording financial transactions in plain English into a double-entry accounting system", "type": "module", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/openai": "latest", "@mastra/core": "^0.10.3", "@mastra/evals": "latest", "@mastra/fastembed": "latest", "@mastra/libsql": "latest", "@mastra/memory": "^0.10.2", "@mastra/pg": "latest", "@supabase/supabase-js": "^2.39.7", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "express": "^5.1.0", "zod": "^3.24.3"}, "devDependencies": {"@types/node": "^22.15.3", "mastra": "latest", "tsx": "^4.19.4", "typescript": "^5.8.3"}}